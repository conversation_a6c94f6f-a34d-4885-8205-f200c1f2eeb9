services:
  baoshu7:
    image: crpi-5ht6itmmkqtumodb.cn-hangzhou.personal.cr.aliyuncs.com/freespirit/baoshu:latest
    container_name: baoshu7
    environment:
      - WECHAT_NAME=baoshu7
      - TZ=Asia/Shanghai
    ports:
      - "3011:3011"
    mem_limit: 2g
    restart: always
    command: ["npm", "run", "start:client"]

#   baoshu8:
#     image: crpi-5ht6itmmkqtumodb.cn-hangzhou.personal.cr.aliyuncs.com/freespirit/baoshu:latest
#     container_name: baoshu8
#     environment:
#       - WECHAT_NAME=baoshu8
#       - TZ=Asia/Shanghai
#     ports:
#       - "3012:3012"
#     mem_limit: 2g
#     restart: always
#     command: ["npm", "run", "start:client"]

#   baoshu10:
#     image: crpi-5ht6itmmkqtumodb.cn-hangzhou.personal.cr.aliyuncs.com/freespirit/baoshu:latest
#     container_name: baoshu10
#     environment:
#       - WECHAT_NAME=baoshu10
#       - TZ=Asia/Shanghai
#     ports:
#       - "3014:3014"
#     mem_limit: 2g
#     restart: always
#     command: ["npm", "run", "start:client"]

#   baoshu11:
#     image: crpi-5ht6itmmkqtumodb.cn-hangzhou.personal.cr.aliyuncs.com/freespirit/baoshu:latest
#     container_name: baoshu11
#     environment:
#       - WECHAT_NAME=baoshu11
#       - TZ=Asia/Shanghai
#     ports:
#       - "3015:3015"
#     mem_limit: 2g
#     restart: always
#     command: ["npm", "run", "start:client"]

  baoshu12:
    image: crpi-5ht6itmmkqtumodb.cn-hangzhou.personal.cr.aliyuncs.com/freespirit/baoshu:latest
    container_name: baoshu12
    environment:
      - WECHAT_NAME=baoshu12
      - TZ=Asia/Shanghai
    ports:
      - "3016:3016"
    mem_limit: 2g
    restart: always
    command: ["npm", "run", "start:client"]
#
#   baoshu13:
#     image: crpi-5ht6itmmkqtumodb.cn-hangzhou.personal.cr.aliyuncs.com/freespirit/baoshu:latest
#     container_name: baoshu13
#     environment:
#       - WECHAT_NAME=baoshu13
#       - TZ=Asia/Shanghai
#     ports:
#       - "3017:3017"
#     mem_limit: 2g
#     restart: always
#     command: ["npm", "run", "start:client"]
#
#   baoshu14:
#     image: crpi-5ht6itmmkqtumodb.cn-hangzhou.personal.cr.aliyuncs.com/freespirit/baoshu:latest
#     container_name: baoshu14
#     environment:
#       - WECHAT_NAME=baoshu14
#       - TZ=Asia/Shanghai
#     ports:
#       - "3018:3018"
#     mem_limit: 2g
#     restart: always
#     command: ["npm", "run", "start:client"]
#
#   baoshu15:
#     image: crpi-5ht6itmmkqtumodb.cn-hangzhou.personal.cr.aliyuncs.com/freespirit/baoshu:latest
#     container_name: baoshu15
#     environment:
#       - WECHAT_NAME=baoshu15
#       - TZ=Asia/Shanghai
#     ports:
#       - "3019:3019"
#     mem_limit: 2g
#     restart: always
#     command: ["npm", "run", "start:client"]
#
#   baoshu16:
#     image: crpi-5ht6itmmkqtumodb.cn-hangzhou.personal.cr.aliyuncs.com/freespirit/baoshu:latest
#     container_name: baoshu16
#     environment:
#       - WECHAT_NAME=baoshu16
#       - TZ=Asia/Shanghai
#     ports:
#       - "3020:3020"
#     mem_limit: 2g
#     restart: always
#     command: ["npm", "run", "start:client"]

  baoshu17:
      image: crpi-5ht6itmmkqtumodb.cn-hangzhou.personal.cr.aliyuncs.com/freespirit/baoshu:latest
      container_name: baoshu17
      environment:
        - WECHAT_NAME=baoshu17
        - TZ=Asia/Shanghai
      ports:
        - "3021:3021"
      mem_limit: 2g
      restart: always
      command: ["npm", "run", "start:client"]

  baoshu18:
      image: crpi-5ht6itmmkqtumodb.cn-hangzhou.personal.cr.aliyuncs.com/freespirit/baoshu:latest
      container_name: baoshu18
      environment:
        - WECHAT_NAME=baoshu18
        - TZ=Asia/Shanghai
      ports:
        - "3022:3022"
      mem_limit: 2g
      restart: always
      command: ["npm", "run", "start:client"]

#   baoshu19:
#         image: crpi-5ht6itmmkqtumodb.cn-hangzhou.personal.cr.aliyuncs.com/freespirit/baoshu:latest
#         container_name: baoshu19
#         environment:
#           - WECHAT_NAME=baoshu19
#           - TZ=Asia/Shanghai
#         ports:
#           - "3023:3023"
#         mem_limit: 2g
#         restart: always
#         command: ["npm", "run", "start:client"]

  baoshu20:
    image: crpi-5ht6itmmkqtumodb.cn-hangzhou.personal.cr.aliyuncs.com/freespirit/baoshu:latest
    container_name: baoshu20
    environment:
      - WECHAT_NAME=baoshu20
      - TZ=Asia/Shanghai
    ports:
      - "3024:3024"
    mem_limit: 2g
    restart: always
    command: ["npm", "run", "start:client"]
  baoshu21:
    image: crpi-5ht6itmmkqtumodb.cn-hangzhou.personal.cr.aliyuncs.com/freespirit/baoshu:latest
    container_name: baoshu21
    environment:
      - WECHAT_NAME=baoshu21
      - TZ=Asia/Shanghai
    ports:
      - "3025:3025"
    mem_limit: 2g
    restart: always
    command: ["npm", "run", "start:client"]
#
#   baoshu22:
#     image: crpi-5ht6itmmkqtumodb.cn-hangzhou.personal.cr.aliyuncs.com/freespirit/baoshu:latest
#     container_name: baoshu22
#     environment:
#       - WECHAT_NAME=baoshu22
#       - TZ=Asia/Shanghai
#     ports:
#       - "3026:3026"
#     mem_limit: 2g
#     restart: always
#     command: ["npm", "run", "start:client"]
#
#   baoshu23:
#     image: crpi-5ht6itmmkqtumodb.cn-hangzhou.personal.cr.aliyuncs.com/freespirit/baoshu:latest
#     container_name: baoshu23
#     environment:
#       - WECHAT_NAME=baoshu23
#       - TZ=Asia/Shanghai
#     ports:
#       - "3027:3027"
#     mem_limit: 2g
#     restart: always
#     command: ["npm", "run", "start:client"]

  baoshu24:
    image: crpi-5ht6itmmkqtumodb.cn-hangzhou.personal.cr.aliyuncs.com/freespirit/baoshu:latest
    container_name: baoshu24
    environment:
      - WECHAT_NAME=baoshu24
      - TZ=Asia/Shanghai
    ports:
      - "3028:3028"
    mem_limit: 2g
    restart: always
    command: ["npm", "run", "start:client"]

  baoshu25:
    image: crpi-5ht6itmmkqtumodb.cn-hangzhou.personal.cr.aliyuncs.com/freespirit/baoshu:latest
    container_name: baoshu25
    environment:
      - WECHAT_NAME=baoshu25
      - TZ=Asia/Shanghai
    ports:
      - "3029:3029"
    mem_limit: 2g
    restart: always
    command: ["npm", "run", "start:client"]