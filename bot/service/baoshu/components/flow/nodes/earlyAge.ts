import { BaoshuWorkFlowNode, trackInvoke } from './baseNode'
import { IWorkflowState } from '../flow'
import { ChatStateStore, ConversationState } from '../../../storage/chat_state_store'
import { WechatMessageSender } from '../../message_send'
import { BaoshuNode } from '../type'

import chalk from 'chalk'
import { InviteToGroupHelper } from './helper/inviteToGroup'
import { LLMNode } from './llm'
import { IWecomMsgType } from '../../../../../lib/juzi/type'
import logger from '../../../../../model/logger/logger'
import { EndNode } from './end'
import { sleep } from '../../../../../lib/schedule/schedule'
import { isUserAgreedToJoinConsultGroupPrompt } from '../../../prompt/baoshu/confirm'
import { LLM } from '../../../../../lib/ai/llm/LLM'
import { XMLHelper } from '../../../../../lib/xml/xml'
import { IsUserPurchaseEarlyAge } from '../../../prompt/baoshu/is_user_purchase_early_age'

export class EarlyAgeInvitePendingNode extends BaoshuWorkFlowNode {
  @trackInvoke
  public static async invoke(state: IWorkflowState) {
    const chatState = ChatStateStore.get(state.chat_id)
    const userStatus = ChatStateStore.getStatus(state.chat_id)
    const userSlots = chatState.userSlots
    const nodeInvokeCount: Record<string, number> = ChatStateStore.get(state.chat_id).nodeInvokeCount

    if (userStatus.consultGroupInvited || userStatus.operationGroupInvited || nodeInvokeCount[EarlyAgeInvitePendingNode.name] > 1) {
      return EarlyAgePurchaseInviteNode.invoke(state)
    }

    return EarlyAgePurchaseInviteNode.invoke(state)
  }
}

export class EarlyAgeInvitedNode extends BaoshuWorkFlowNode {
  @trackInvoke
  public static async invoke(state: IWorkflowState) {
    const status = ChatStateStore.getStatus(state.chat_id)

    if (status.consultGroupEntered || status.operationGroupEntered) {
      logger.debug('用户已经进群，进入通知用户直接跟顾问沟通阶段')
      return BaoshuNode.END
    }

    // 根据过去的对话，判断用户是否同意进群
    const isAgree  = await InviteToGroupHelper.isUserAgreeToEnterGroup(state) // 'true' | 'false' | 'not yet'
    logger.debug({ chat_id: state.chat_id }, chalk.redBright(`用户是否同意进群：${ isAgree }`))

    const nodeRound = ChatStateStore.getNodeEnterCount(state.chat_id, EarlyAgeInvitedNode.name)

    // 顾问群处理
    if (status.consultGroupInvited) {
      if (isAgree === 'true' ||  (nodeRound > 0 && isAgree !== 'false')) {
        await InviteToGroupHelper.inviteToCounselor(state, true)
        return BaoshuNode.END
      } else if (isAgree === 'not yet') {
        // 再问一次
        // 用户未同意进群，可以再询问下用户还有什么问题，然后再次邀请
        await LLMNode.invoke({
          state,
          dynamicPrompt: '你当前已经邀请过用户进入顾问群，用户仍处于没有直接同意或犹豫的状态，你可以先回答用户问题， 然后以你很忙，时间不够的方式，让顾问老师帮助规划的方式再次引导用户进群。或者参考这个话术："咱们的情况需要进一步的询问和讨论。我还是建议让专业的顾问老师跟你沟通下，聊聊免费的，了解清楚了才能给你最佳的方案。稍等。我拉群给你。"',
          referenceChatHistory: true,
          noInterrupt: true
        })

        await InviteToGroupHelper.inviteToCounselor(state, true)
        return BaoshuNode.END
      } else {
        await WechatMessageSender.sendById({
          user_id: state.user_id,
          chat_id: state.chat_id,
          ai_msg: `您也可以在我直播的时候进我的直播间，跟我连麦。
我直接跟您解答。`
        })

        return BaoshuNode.END
      }
    } else if (status.operationGroupInvited) {
      // // 运营群
      // if (isAgree === 'true') {
      //   logger.debug('用户同意进群，发送运营群二维码')
      //   await WechatMessageSender.sendById({
      //     user_id: state.user_id,
      //     chat_id: state.chat_id,
      //     ai_msg: '[[进群二维码]]',
      //     send_msg: {
      //       type: IWecomMsgType.Image,
      //       url: 'https://free-spirit-static.oss-cn-hangzhou.aliyuncs.com/551719208316_.pic_hd.jpg'
      //     }
      //   })
      //
      //   await WechatMessageSender.sendById({
      //     user_id: state.user_id,
      //     chat_id: state.chat_id,
      //     ai_msg: '可以扫码进群，群里会不定期分享资源'
      //   })
      //
      //   // 更新进群状态
      //   ChatStateStore.update(state.chat_id, {
      //     state: ConversationState.OperationGroupEntered,
      //     status: {
      //       operationGroupInvited: true,
      //       operationGroupEntered: true
      //     }
      //   })
      // } else {
      //   // 用户不同意进群，结束对话
      //   await LLMNode.invoke({
      //     state,
      //     dynamicPrompt: '当前用户不同意进群，礼貌的结束对话',
      //   })
      // }

      await EndNode.invoke(state)

      return BaoshuNode.END
    } else {
      throw new Error('未知的进群状态')
    }
  }
}

export class EarlyAgePurchaseInviteNode extends BaoshuWorkFlowNode {
  @trackInvoke
  public static async invoke(state: IWorkflowState) {
    const status = ChatStateStore.getStatus(state.chat_id)


    // 超过3轮自动终止
    if (ChatStateStore.getNodeEnterCount(state.chat_id, EarlyAgePurchaseInviteNode.name) > 3) {
      return BaoshuNode.END
    }

    if (!status.is_send_early_age_purchase_msg) {
      await WechatMessageSender.sendById({
        user_id: state.user_id,
        chat_id: state.chat_id,
        ai_msg: `针对孩子目前的情况，光靠几句话很难给你确切的建议。建议你直接购买我们的1对1咨询（500元），长按识别海报上的二维码购买，就能为咱们孩子匹配一位专属老师，好好地把情况聊透，拿到一份清晰的规划路线，心里就踏实了。
购买成功后，回复“已购买”，会给咱们拉个群，匹配资深顾问老师进行接下来的服务，稳了！`
      })

      await sleep(2000)
      await WechatMessageSender.sendById({
        user_id: state.user_id,
        chat_id: state.chat_id,
        ai_msg:'低龄咨询购买二维码',
        send_msg:{
          type: IWecomMsgType.Image,
          url: 'https://free-spirit-static.oss-cn-hangzhou.aliyuncs.com/baoshu/%e4%bd%8e%e9%be%84%e4%b8%8b%e5%8d%95%e4%ba%8c%e7%bb%b4%e7%a0%81.jpg'
        }
      })

      ChatStateStore.update(state.chat_id, {
        status: {
          is_send_early_age_purchase_msg: true
        }
      })
    }


    if (status.is_send_early_age_purchase_msg) {

      const llm = new LLM()
      const llmRes = await llm.predict(await IsUserPurchaseEarlyAge.format(state.userMessage))

      const xmlAnswer = XMLHelper.extractContent(llmRes, 'result')

      if (xmlAnswer === 'YES') {
        await WechatMessageSender.sendById({
          user_id: state.user_id,
          chat_id: state.chat_id,
          ai_msg: '好的，现在就给咱们拉群哈'
        })
        await InviteToGroupHelper.inviteToCounselor(state, true)
        return BaoshuNode.END
      } else {
        await LLMNode.invoke({
          state,
          dynamicPrompt: '你当前已经邀请过用户购买1v1深度咨询，用户仍处于未购买状态，你可以先回答用户问题，然后邀约用户购买',
          referenceChatHistory: true,
          noInterrupt: true
        })
      }


    }



    return BaoshuNode.EarlyAgePurchaseInvite
  }
}