import { Config } from '../../../../config/config'
import { HumanTransfer } from '../human_transfer'
import { ChatHistoryService, IDBBaseMessage } from '../chat_history'
import { PrismaMongoClient } from '../../../../model/mongodb/prisma'
import { InviteToGroupHelper } from '../flow/nodes/helper/inviteToGroup'

describe('Test', function () {
  beforeAll(() => {
    Config.setting.wechatConfig =   {
      orgToken: 'e0d70927040a4efa92b79b7279ecb1c1',
      id: '1688854546332791',
      botUserId: 'ShengYueQing',
      name: '暴叔',
      counselorIds: [],
      notifyGroupId: 'R:10735753744477170'
    }
  })

  it('标签更新', async () => {
    console.log(JSON.stringify(await (HumanTransfer as any).updateTags('wm41XZJQAAraXYGQlIcWplXS58GTqrew', 'AI off'), null, 4))
  }, 60000)

  it('测试', async () => {
    console.log(JSON.stringify(await ChatHistoryService.getRecentConversations('7881301047907394_1688854546332791', 3), null, 4))
  }, 60000)

  it('sda', async () => {
    const chatHistory = await PrismaMongoClient.getInstance().chat_history.findMany({
      where: {
        chat_id: '7881303465060553_1688858120470536'
      },
      orderBy: {
        created_at: 'asc'
      },
    }) as IDBBaseMessage[]

    console.log(JSON.stringify(chatHistory, null, 4))
  }, 60000)

  it('123123', async () => {
    console.log(JSON.stringify(await ChatHistoryService.getChatHistory('7881300295110423_1688858120470536'), null, 4))
    await ChatHistoryService.deleteByIds(['66fdfaa473b960c27a5843dc', '66fdfaac73b960c27a5843e2', '66fdfab073b960c27a5843e5' ])
  }, 60000)

  // it('测试分组选择老师', async () => {
  //   for (let i = 0; i < 60; i++) {
  //     console.log(await InviteToGroupHelper.getCounselorGroupInGaoKaoPeriod())
  //   }
  // }, 60000)
})